#!/bin/bash

# Test script to verify the argument parsing of update_gandalf_unified.sh
# This script tests various scenarios without actually running the update

echo "=== Testing update_gandalf_unified.sh argument parsing ==="
echo ""

SCRIPT_PATH="admin/update_gandalf_unified.sh"

if [ ! -f "$SCRIPT_PATH" ]; then
    echo "❌ Script not found: $SCRIPT_PATH"
    exit 1
fi

echo "✅ Found script: $SCRIPT_PATH"
echo ""

# Test 1: No arguments (should show usage)
echo "--- Test 1: No arguments ---"
bash "$SCRIPT_PATH" 2>&1 | head -5
echo ""

# Test 2: Help flag
echo "--- Test 2: Help flag (-h) ---"
bash "$SCRIPT_PATH" -h 2>&1 | head -5
echo ""

# Test 3: Invalid flag
echo "--- Test 3: Invalid flag (-x) ---"
bash "$SCRIPT_PATH" -x 2>&1 | head -3
echo ""

# Test 4: Missing argument for -b
echo "--- Test 4: Missing argument for -b ---"
bash "$SCRIPT_PATH" -b 2>&1 | head -3
echo ""

# Test 5: Valid branch argument (dry run - we'll stop before actual execution)
echo "--- Test 5: Valid branch argument ---"
echo "Note: This would normally proceed with 'main' branch"
echo "Command: $SCRIPT_PATH -b main"
echo ""

echo "=== Argument parsing tests completed ==="
