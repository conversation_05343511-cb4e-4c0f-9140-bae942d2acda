name: Remote API Status Check

on:
  pull_request:
    branches:
      - main
      - Hotspot_Demo
  push:
    branches:
      - main
      - Hotspot_Demo

jobs:
  test-remote-api:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12' # or your Python version

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests

      - name: Set up environment file
        run: |
          echo "GANDALF_API_KEY=${{ secrets.GANDALF_API_KEY }}" > .env

      - name: Run Gandalf Remote API tests
        run: |
          python test_remote_api.py
