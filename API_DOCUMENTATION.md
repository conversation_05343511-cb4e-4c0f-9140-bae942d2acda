# Gandalf API Documentation

## Base URL
- Production: https://gandalf.esvc.us
- Development: http://localhost:8001

## Authentication
All requests require an API key in the `X-API-Key` header.

## Endpoints

### Router Management

#### Create Router Rental
```
POST /router/{ip}/rental
```
- Path Parameters:
  - `ip`: Router IP address
- Query Parameters:
  - `unit`: Unit identifier
  - `password`: Network password
  - `package`: Package type
- Response: `{"message": string}`

#### Reset Access Point
```
POST /router/{ip}/reset
```
- Path Parameters:
  - `ip`: Router IP address
- Response: `{"actions": array}`

### Network Management

#### Create Network
```
POST /network/new
```
- Body Parameters:
  ```json
  {
    "unit": string,
    "password": string,
    "days": integer,
    "package": string,
    "email": string,
    "price": float
  }
  ```
- Response: `{"order_id": string, "message": string}`

#### Get Active Networks
```
GET /networks/active
```
- Response: `{"networks": array}`

[Continue documenting other endpoints...]