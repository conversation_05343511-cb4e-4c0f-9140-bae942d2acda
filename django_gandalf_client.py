"""
Django client for Gandalf API - Updated to match latest gandalf_api.py function signatures.

Key updates from previous version:
- Updated return type annotations to match current API response models
- Enhanced method documentation with detailed response structures
- Updated SecurityProfileResponse to include rental_order_info structure
- Added proper type hints for RouterInfo, SSIDInfo, and QueueConfig models
- Updated SaveQueryRequest to use tuple instead of list for params
- All method signatures now match the latest API endpoints and response formats
"""

import requests
from django.conf import settings
from typing import Any, Dict, List, Optional, Union


class GandalfAPIError(Exception):
    """Custom exception for errors returned by the Gandalf API."""
    pass


class GandalfAPIClient:
    """
    Client for interacting with the Gandalf FastAPI service.
    Authenticates requests using an API key via the `x-api-key` header.

    Settings expected:
    - GANDALF_API_URL: Base URL of the Gandalf API (e.g., "http://127.0.0.1:8001").
    - GANDALF_API_KEY: API key for authentication.
    """

    def __init__(
            self,
            api_url: Optional[str] = None,
            api_key: Optional[str] = None,
            timeout: int = 10
    ):
        # Use Django settings if not explicitly provided
        self.base_url = api_url or settings.GANDALF_API_URL
        self.api_key = api_key or settings.GANDALF_API_KEY
        self.timeout = timeout

        # Prepare a session with default headers
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-API-Key": self.api_key,  # Updated to match API's expected header case
        })

    def _handle_response(self, response: requests.Response) -> Any:
        """
        Internal helper to process HTTP responses.
        Raises GandalfAPIError on HTTP errors, otherwise returns JSON or text.
        """
        try:
            response.raise_for_status()
        except requests.HTTPError as exc:
            # Extract error detail if possible
            try:
                detail = response.json().get("detail", response.text)
            except ValueError:
                detail = response.text
            raise GandalfAPIError(f"[{response.status_code}] {detail}") from exc

        # Attempt to parse JSON, fallback to raw text
        try:
            return response.json()
        except ValueError:
            return response.text

    def _get(self, path: str, params: Dict[str, Any] = None) -> Any:
        url = f"{self.base_url}{path}"
        response = self.session.get(url, params=params, timeout=self.timeout)
        return self._handle_response(response)

    def _post(self, path: str, json: Dict[str, Any] = None, params: Dict[str, Any] = None) -> Any:
        url = f"{self.base_url}{path}"
        response = self.session.post(url, json=json, params=params, timeout=self.timeout)
        return self._handle_response(response)

    def _put(self, path: str, json: Dict[str, Any] = None, params: Dict[str, Any] = None) -> Any:
        url = f"{self.base_url}{path}"
        response = self.session.put(url, json=json, params=params, timeout=self.timeout)
        return self._handle_response(response)

    # Utility endpoint
    def ping(self) -> Dict[str, str]:
        """
        Health check endpoint.

        Returns:
            Dict containing status and timestamp indicating API is operational
            Example: {"status": "ok", "timestamp": "2024-01-01T12:00:00.000000"}
        """
        return self._get("/ping")

    # Unit endpoints
    def get_unit_router_ip(self, unit: str) -> str:
        """Get the router IP for a specific unit."""
        return self._get(f"/unit/{unit}/router-ip")

    def get_unit_orders(self, unit: str) -> List[Dict[str, Any]]:
        """Get all orders for a specific unit."""
        return self._get(f"/unit/{unit}/orders")

    def get_unit_router_profile(self, unit: str) -> Dict[str, Any]:
        """
        Get router profile information for a specific unit.

        Args:
            unit: Unit identifier (without 'et' prefix)

        Returns:
            Router profile information
        """
        return self._get(f"/unit/{unit}/router-profile")

    def security_profile_exists(self, unit: str) -> Dict[str, Any]:
        """
        Check if security profile exists on router and get rental order information.

        Args:
            unit: Unit identifier

        Returns:
            SecurityProfileResponse dict with structure:
            {
                "exists": bool,
                "rental_order_info": Optional[List[Dict]] - List of rental order info if profile exists
            }
            Each rental order info contains: order_id, et_days, package, created_at, hard_expiration_date, isTest, email, isPaid
        """
        return self._get(f"/unit/{unit}/security-profile-exists")

    # Order endpoints
    def update_order(
            self,
            order_id: int,
            transaction_id: str,
            receipt_url: str,
            connect_net: bool = False
    ) -> Any:
        """
        Update an existing order with transaction details.
        
        Args:
            order_id: Order identifier
            transaction_id: Payment transaction ID
            receipt_url: URL to payment receipt
            connect_net: Whether to connect the network immediately
        
        Returns:
            Updated order information
        """
        payload = {
            "order_id": order_id,
            "transaction_id": transaction_id,
            "receipt_url": receipt_url,
            "connect_net": connect_net
        }
        return self._post("/order/update", json=payload)

    # Network rental endpoints
    def new_network(
            self,
            unit: str,
            password: str,
            days: int,
            package: str,
            email: str,
            price: float,
            hard_expiration_date: Optional[str] = None,
            isTest: Optional[int] = 0,
            origin: Optional[str] = None,
            create_net: Optional[bool] = False
    ) -> Dict[str, Any]:
        """
        Create a new network rental order.

        Args:
            unit: Unit identifier
            password: Network password
            days: Duration in days
            package: Network package type
            email: Customer email
            price: Package price
            hard_expiration_date: Optional hard expiration date (YYYY-MM-DD)
            isTest: Flag to categorize order as test environment (0 or 1)
            origin: Server processing this rental
            create_net: Whether to create network immediately

        Returns:
            Dict containing order_id and message
        """
        payload = {
            "unit": unit,
            "password": password,
            "days": days,
            "package": package,
            "email": email,
            "price": price,
            "hard_expiration_date": hard_expiration_date,
            "isTest": isTest,
            "origin": origin,
            "create_net": create_net
        }
        return self._post("/network/new", json=payload)

    def get_orders_mtd(self) -> List[Dict[str, Any]]:
        """
        Get month-to-date orders.
        
        Returns:
            List of order dictionaries including isTest field
        """
        return self._get("/orders/month-to-date")

    def get_recent_orders(self) -> List[Dict[str, Any]]:
        """
        Get recent order info.
        
        Returns:
            List of recent order dictionaries including isTest field
        """
        return self._get("/orders/recent")

    def get_active_networks(self) -> List[Dict[str, Any]]:
        """
        Get all active network rentals.

        Returns:
            List of active network dictionaries including isTest field
        """
        return self._get("/networks/active")

    def get_orders_count(self) -> Dict[str, int]:
        """
        Get the count of orders in the database.

        Returns:
            Dict containing orders_count
        """
        return self._get("/orders/count")

    # Router management endpoints
    def get_routers(self) -> List[Dict[str, Any]]:
        """List all routers from database."""
        return self._get("/routers")

    def reset_access_point(self, ip: str) -> Dict[str, Any]:
        """
        Reset a router's rental configuration.

        Args:
            ip: Router IP address

        Returns:
            Dict with actions performed: {"actions": [...]}
        """
        return self._post(f"/router/{ip}/reset")

    def reset_ap_by_unit(self, unit: str) -> Dict[str, Any]:
        """
        Reset a router's rental configuration by unit identifier.

        Args:
            unit: Unit identifier (without 'et' prefix)

        Returns:
            Dict with actions performed: {"actions": [...]}

        Raises:
            GandalfAPIError: If unit not found or reset fails
        """
        return self._post(f"/unit/{unit}/reset")

    def add_queue(self, ip: str, queue_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Add or update queue configuration on a router.

        Args:
            ip: Router IP address
            queue_config: QueueConfig dict with structure:
                {
                    "name": str,
                    "max_limit": str,
                    "target": str,
                    "queue": str,
                    "burst_limit": str,
                    "burst_threshold": str,
                    "dst": str,
                    "burst_time": str
                }

        Returns:
            Dict with status and message: {"status": "success", "message": "Queue configuration updated"}
        """
        return self._post(f"/router/{ip}/queue", json=queue_config)

    def get_queue_info(self, ip: str) -> Any:
        """
        Retrieve queue configuration from a router.

        Args:
            ip: Router IP address

        Returns:
            Queue configuration data from the router
        """
        return self._get(f"/router/{ip}/queue")

    def get_ssid_info(self, ip: str) -> List[Dict[str, Any]]:
        """
        Retrieve SSID details from a router.

        Args:
            ip: Router IP address

        Returns:
            List of SSIDInfo dicts, each with structure:
            {
                "ssid": str,
                "password": str,
                "master_interface": str,  # Converted from "master-interface"
                "security_profile": str,  # Converted from "security-profile"
                "mac_address": str,  # Converted from "mac-address"
                "band": str,
                "channel_width": str,  # Converted from "channel-width"
                "frequency": str
            }
            Note: The API automatically converts hyphenated field names to underscore format.
        """
        return self._get(f"/router/{ip}/ssid")

    def get_registration_list(self, ip: str) -> Any:
        """
        Get the wireless registration table from a router.

        Args:
            ip: Router IP address

        Returns:
            Wireless registration table data from the router
        """
        return self._get(f"/router/{ip}/registrations")

    def get_router_info(self, ip: str) -> Dict[str, Any]:
        """
        Get detailed router information.

        Args:
            ip: Router IP address

        Returns:
            RouterInfo dict with structure:
            {
                "hostname": str,
                "model": str,
                "firmware": str,
                "rental": bool,
                "ssid_24": str,
                "freq_24": Union[int, str],  # Can be "auto" or frequency number
                "freq_5": Union[int, str],   # Can be "auto" or frequency number
                "ssid_5": str,
                "ssid_pwd": str,
                "ssid_clients": int,
                "queue_name": str,
                "ipaddress": str
            }
        """
        return self._get(f"/router/{ip}/info")

    def new_rental(
            self,
            ip: str,
            unit: str,
            password: str,
            package: str,
    ) -> Dict[str, str]:
        """
        Create a new rental configuration on a router via query params.

        Args:
            ip: Router IP address
            unit: Unit identifier
            password: Rental password
            package: Package type (standard/streaming)

        Returns:
            Dict with status and message: {"status": "success", "message": "..."}
        """
        params = {"unit": unit, "password": password, "package": package}
        return self._post(f"/router/{ip}/rental/new", params=params)

    def router_turn_rental_off(self, ip: str) -> Dict[str, Any]:
        """
        Disable rental status on a router.

        Args:
            ip: Router IP address

        Returns:
            Dict with status, message and affected_rows count
        """
        return self._put(f"/router/{ip}/rental/off")

    def save_router(self, router_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save or update router information in database.

        Args:
            router_data: RouterInfo dictionary containing router information with fields:
                hostname (str), model (str), firmware (str), rental (bool),
                ssid_24 (str), freq_24 (Union[int, str]), ssid_5 (str),
                freq_5 (Union[int, str]), ssid_pwd (str), ssid_clients (int),
                queue_name (str), ipaddress (str)

        Returns:
            Dict with status, message and affected_rows count:
            {"status": "success", "message": "Router information saved successfully", "affected_rows": int}
        """
        # The API expects rental as a boolean, not int
        # Ensure proper type conversion if needed
        if "rental" in router_data and isinstance(router_data["rental"], int):
            router_data["rental"] = bool(router_data["rental"])

        return self._post("/router/save", json=router_data)

    def update_nas(self, hostname: str, ipaddress: str) -> Dict[str, str]:
        """
        Update IP address for a NAS device.

        Args:
            hostname: NAS hostname (e.g., 'et708')
            ipaddress: New IP address for the NAS device

        Returns:
            Dict with status and message: {"status": "success", "message": "Updated IP address for {hostname} to {ipaddress}"}
        """
        return self._put(f"/nas/{hostname}/ip/{ipaddress}")

    def get_router_hostname(self, ip: str) -> str:
        """
        Get the hostname of a router.

        Args:
            ip: Router IP address

        Returns:
            Router hostname
        """
        result = self._get(f"/router/{ip}/hostname")
        # API returns {"hostname": "value"}, extract the hostname
        if isinstance(result, dict) and "hostname" in result:
            return result["hostname"]
        return result
        
    # Internal DB operations
    def save_query(
            self,
            query: str,
            params: Optional[tuple] = None,
            return_new_id: bool = False
    ) -> Union[int, Dict[str, Any]]:
        """
        Execute a save query on the database.

        Args:
            query: SQL query string with placeholders
            params: Tuple of parameters to substitute in the query (matches SaveQueryRequest model)
            return_new_id: Whether to return the ID of newly inserted row

        Returns:
            Either lastrowid (int) or rowcount (int) depending on return_new_id flag
        """
        payload = {
            "query": query,
            "params": params,
            "return_new_id": return_new_id
        }
        return self._post("/internal/db/save", json=payload)

    def ping_hosts(self, ip_list: List[str]) -> Dict[str, str]:
        """
        Ping multiple hosts and return their status.
        
        Args:
            ip_list: List of IP addresses to ping
        
        Returns:
            Dict mapping IP addresses to HTML status fragments
        """
        return self._post("/ping-hosts", json=ip_list)